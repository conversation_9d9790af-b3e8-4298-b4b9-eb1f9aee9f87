
# 0.1 - ✅
- [x] Timer
- [ ] Base stats 
    - [x] weeks
    - [ ] days
- [ ] Projects
    - [ ] fix  "create projects"
    - [ ] рабочие vs личные? 
        Надо подумать отдельно. Чтобы потом в статистике можно было показать "только рабочие"
- [x] Сделать блок для идей по результатам тестирования
- [ ] Сделать проект "без категории" - пока вручную
- [ ] Нормальное отображение статистических пкоазктелей 
    - Среднее в день. Ьам если 0.3, то он показывает 3. 
    - ВРемя старта тоже показывает без ":"

# 0.2 - ▶️
## Improovements (по результатам использования)
### A-grade
- [ ] После начала отдыха сделать таймер (17) - а то многие нажмут "закончить" а сами работают
    - И напоминать закрыть ноутбук и идти гулять
    - И можно отслеживать бы ли открыт ноут во время интервалов и отображать в статистике и в рекомендациях
    - После истечения таймера отдыха не хватает окна "начать интервал?"
- [ ] В статистике выборка по проектам 
- [ ] Более агрессивные сообщения во вскплывающем окне (можно утвердить их список)
    - Чтобы они говорили о важности перерывов
- [ ] Пользователи. Авторизация и БД

### B-grade (later)
- [ ] Возможность выбирать выходные (и рабочие дни)
- [ ] Если буду игнорировать эти напоминания. 
    То можно будет что-то с этим делать. Например на весь экран окно, с невозможностью закрыть и с кратким напоминанием к чему ведёт переработка (потеря уверенности, снижение продуктивности, тревожность, отсутствие наслаждения). Сделай вдох и или погуляй. Никуда от тебя работа не денется.
- [ ] Как начинать работу с нуля
    два элемента - работа - первое дело дня. И практика Павла психитра - делать работу или ничего 
- [ ] Улучшение статистических блоков
    - На недельном 
    - На дневном
    - Без категории (любые идеи по статистическим блокам) - чем больше данных собираем, тем легче будет анализировать
        - Среднее время отдыха (поможет получше понять жффеткивность)

### Непонятно (но проблема есть)
- [ ] Что если надо прекратить на половине интервала? Кушать например. 
- [ ] Идет ли таймер если закрыть крышку ноута? 









# OLD (messy)

## 0.1 - pomodoro
- [x] base timer
- [x] Bug: после нажатия +1 мин не появилось окно с напоминанием через 1 минуту. Нужно починить
- [x] Если переработка, то текст таймера меняется. Слегка желтый после 1-й минуты. Орандевый после 3-х минут. И красный после 5 минут. И темно бардовый после 10 минут. После 15 минут появляется смайлик с закипающим мозгом 😤
- Анимация кнопок "заверщить" и продлить"
- [x] Base stats (сколько интевалов полноценных (именно 52 минуты прошло, а не заверешнных вручную ) за сегодня, вчера, неделю, месяц, год, всего за все время)
    - [x] Страница статистики. Пока что простая. Тупо сколько интервалов за день, неделю, месяц, год, всего за все время. Графики не нужны. Тоже самое для переработки. 
    - [x] Как анализировать и давать рекомендации? Спроси ЛЛМ. Анализ постоянства, поздно начинает, перерабатыает, риск выгорания. 
- [ ] Графические элементы. Непонятно пока как. Либо просто в виде интервалов, с переработками. Либо статистику по дням. Например если это неделя, то 7 столбиков. И в каждом. Тут надо сначала подумать для недель как показать. Не надо сразу для всех периодов думать. Тут много вариаций даже для недели. Как показывать? Просто интервалы? Нет. 
    - [x]разные цвета в зависимости от кол-ва интервалов. До 1-2 синий. От 3 до 5 зеленый.  6 желтый.  7 оранжевый. От 8 и выше красный. 
    - Легенда по дням (ось X) и кол-ву интервалов Y























- [ ] Проекты
    - Сделать проект "без категории". Третий. Когда просто открываем комп и непонятно что делаем. 
- [ ] Контекстное меню не стандартное, а кастомизированное. Чтобы дизайн можно было настраивать. И Там сделать разные версии проектов. С возможностью быстрой смены проекта.  И СПИСОК ЗАДАЧ GG НА ДЕНЬ - ЧЕКЛИСТЫ ПО ПЛАНКАМ. Кол-во интервалов сегодня. СТрики может быть. Скор. (мини-дашборд короче). Да, многих элементов нету, но надо создать болванку, на которую потом буду насаживать. 













- Для каждого статистического блока можно сделать четыре вида градации, красненькую, оранжевую, желтую, зеленую. То есть в виде батарейки или как у меня вью мема есть для индикатор новых слов.
- Вообще подумать как сделать для дней, как для неделей, месяцев и лет. Разные или одинаоквые элементы будут? 
    - [ ] Статистика для дней. Там иначе все будет. Подумать как. Я буду тестить каждый день на реальной работе. И смотреть как он спавится. 
        - Если все хорошо, то говорить что все хорошо. Нет рекомендаций, продолжай в том же духе
    - чарты (если месячные, то можно сделать выборку по дням, а можно по неделям). Годовые тоже самое. Можно по месяцам, можно по неделям. 
- Подумать какие показатели считать в статистике. Более детально. 
    - Может сделать Score-постоянства справа обособленным. Как стрелка бензина. Или как линию в пространстве, чтобы удерживать ее. 

- 🌡️ SCORE
    Возможно будет состоять из нескольких показателей
    - 1. Постоянство (сколько дней за неделю работал) 4 из 5? Значит 0.8 (80%)
    - 2. Стабильность итервалов (нет резких скачков)
    - 2.5. Нормальное кол-во интервалов в день. 4-5. Чем меньше, тем хуже показатель. Чембольше, тем тоже хуже.  
    - 3. Нет переработок 
    И отследивать его во времени. По неделям. И выводить в дашборде как кривую. Кстати, можно его также оценивать и во времени.
- Проблема - нет выходных. В анализ недели и месяца. В настройки - работать в выходные? По крайней мере в субботу. Да, короткий день. Но лучше не надо. 
- Критические рекомендации. И допустимые. Например перербаотка +5-10 минут к интервалу это ок. 10-20 это уже медиум. Более это уже хай-риск. Потом По кол-ву интервалов в день. 6 это уже низкий риск. Более это уже критично. И так каждый элемент разобрать. Время начала. 8-9 это лоу. 9-10 это медиум. После 10 это уже хай риск. Возможно кстати давать в самих плашках оценку риска. И рекомендации. А то таблицы прям не очень. 
- Проекты. Описать что хочу и Спросить как
- Не даст начать интервал, если не прошло время отдыха. 17 минут для коротких и 60-90 для длинного. 
    - + сообщение, что не прошло время отдыха
    - Кнопка "начать все равно, осознаю риски"
- Звуки 
    - заверщения интервала
    - продлений (1, 2, 3) - агрессивнее
    - возможность выбора звука в настрйоках
- Конфети после звершения интервала
- Анимации окна
- Время переработки (дублировать таймер) в окно 
- После завершения отдыха предложит начать интервал, в таком же всплывающем окне, но другом.
- окно всплывающее более прозрачным. И с глассморфизмом
- Если окошко открыто в оверворке, то можно сделать его анимацию короткую, что мол иди отдыхай
- Кнопка "начать отдых" тоже нужна. С таймером. Не у всех часы есть. Но надо сделать интеграцию с телефоном. Потому что если ноут закрыт то что? Но это потом. Пока для себя делаю. 
- БД и авторизация


Так, некоторые элементы статистики можно скрыть, такие как например время начала, среднее время, там окончание работ. Они как бы, ну или там сделать их поменьше, просто они такие не основные, но они используются для рекомендаций. Тоже, потому что если там человек заканчивает работу там ночью, то ну как бы хреново ему будет, потом быстро. Вот и 

Сами рекомендации тоже давать можно как бы кратко, то есть сделать просто их список, приоритизировать, но это в документации, а в самом приложении давать кратко и там давать какую-то типа там, смотреть, читать подробнее или там, ну не знаю, как это сделать красиво. Там уже подробнее рассказывать, для чего это нужно, почему это важно и уже прям совсем, если они хотят подробнее, то уже там переводить на какую-то там страничку в интернете, где рассказывать, почему это вообще важно, прям там уже, ну обычная статья, типа там мини-превычки, все такое, то есть получается краткая информация, просто краткий, сначала краткая, прям краткая, краткая, потом чуть поподробнее, если надо и уже прям совсем подробнее тоже на сайте.



## 0.2 - авторизация для сохранения статистики

## 0.3 - score постоянства
- Как считать? Не знаю. Расскажи что думаешь для ЛЛМ и спроси решение


===
- Короткий интервал (25 минут) - прям в контекстном меню, под основным. И который кстати будет перерастать в обычный
- После 12 создаются короткие

## Адаптивный интервал
- Для тех, кто не может начать - 1-5 минут. Или вообще прям целая программа влючения в поток. Нажимает SOS и вводит в поток. Хотя можно и без нажатия SOS это делать. Видит что давно не работал. Значит выпал очевидно. Значит надо возвращать. Начать с малого и наращивать, собирая обратную связь. 

    - То есть программа видит, она там, ты выпал, да, она предлагает укороченный интервал. Если ты и его не начинал, то она предлагает еще более укороченный, там, 5 минут поработать, там, и каждый раз она предлагает, давай там, хотя бы там, задача как-то там, посмотрим, что у нас есть, на чем мы застряли, или там, если она там, чувствую, что застрял, вот, давай попробуем алгоритм преодоления барьеров и так далее, то есть разные варианты пробовать. 
    - Кстати, можно вгнедрить методику Павла психиатора - делай дело или ничего. С мини-обучением. 

Что-то неплохо было бы вот так вот начать работать, да, то есть человек сидит, не может собраться и ему так баться, сообщения всплывать. Ну что-то там я вижу ты уже два дня не можешь начать, давайте по работе. И он говорит, ну и там адаптируется интервал уже естественно, там хотя бы 15 минут ему предлагается. Он говорит, нет, типа у меня нету идей там или что-нибудь такое. И ему там вариант предлагается, там сразу же, давай там поищем идей, там и все такое. Или как-то там я застрял, то есть в зависимости от ему подставляются варианты. И то есть увлекает его в процесс, вот это прям очень важно. То есть мы из любого дерьма его вот так скажем, из переработок, из барьеров, из нет идей и так далее и тому подобное.

- Можно вводить мини-обучения. 
    - Как отдыхать (без тиктока, экранов)

## Как быть с опросами? 
Непонятно куда из внедрять. Не зочется отвечать на вопросы. Даже мне. А юзерам тем более
Варианты 
    - внедрить вопросы в процессы. И вовлекать.
    - просто вечером задавать один вопрос (устал ли), потом другой 

## Стрики 
Стрики, наверное, будут не в статистике, а просто где-то наверху, как в дуалингу, в принципе, сделаны. И также с темы уведомлений будет каждый вечер, что типа, "Эй, дружок, там осталось только то времени, давай" типа один оторвал хотя бы, если он не сделал, конечно, если сделал, то все хорошо. И также его учим, что типа, стрики это вообще как бы входит в топ-3, чуваков с высокой продуктивностью, то есть они выполняют стри, типа, "это мы вы выполняете". Типа, если вы будете там хотя бы минималочку делать, там каждый день 20 минут, то вы через год все они узнаете. Ну и там можно в кратской мини-привычке книгу пересказать там на паре экранах, а даже на одну.

-------------

# v1 - Task-manager 
Попробовать месяцок, может получится что-то интересное. Для лчиного использования. 
- Сделать анализ всех основных конкурентов (что нравится, что не нравится)
- Но надо будет решить главную проблему - как показывать все правильно без путаницы с тасками и подтасками. 
    - Прям сфокусироваться на этой проблеме, нырнуть в нее. Описать что бесит. И посоветоваться как решить. 


# IDEAS
- Сделать интеграцию с другими моими приложениями. Ч